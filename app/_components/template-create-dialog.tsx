'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Plus, X } from 'lucide-react';

interface TemplateField {
  type: string;
  required: boolean;
  description?: string;
  properties?: Record<string, TemplateField>; // For object types
  items?: TemplateField; // For array types
}

interface TemplateCreateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (template: {
    name: string;
    description?: string;
    documentType: string;
    fields: Record<string, TemplateField>;
  }) => Promise<void>;
  loading?: boolean;
}

const FIELD_TYPES = [
  { value: 'string', label: 'Text' },
  { value: 'number', label: 'Number' },
  { value: 'date', label: 'Date' },
  { value: 'email', label: 'Email' },
  { value: 'phone', label: 'Phone' },
  { value: 'array', label: 'List' },
  { value: 'object', label: 'Object' },
];

const DOCUMENT_TYPES = [
  { value: 'invoice', label: 'Invoice' },
  { value: 'receipt', label: 'Receipt' },
  { value: 'business_card', label: 'Business Card' },
  { value: 'form', label: 'Form' },
  { value: 'contract', label: 'Contract' },
  { value: 'other', label: 'Other' },
];

export function TemplateCreateDialog({
  open,
  onOpenChange,
  onSubmit,
  loading = false,
}: TemplateCreateDialogProps) {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [documentType, setDocumentType] = useState('');
  const [fields, setFields] = useState<Record<string, TemplateField>>({});
  const [newFieldName, setNewFieldName] = useState('');
  const [newFieldType, setNewFieldType] = useState('string');
  const [newFieldRequired, setNewFieldRequired] = useState(false);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [arrayItemType, setArrayItemType] = useState('string');

  const handleAddField = () => {
    if (!newFieldName.trim()) return;

    const newField: TemplateField = {
      type: newFieldType,
      required: newFieldRequired,
    };

    // Add sub-structure for object and array types
    if (newFieldType === 'object') {
      newField.properties = {};
    } else if (newFieldType === 'array') {
      newField.items = {
        type: arrayItemType,
        required: false,
        ...(arrayItemType === 'object' ? { properties: {} } : {}),
      };
    }

    setFields((prev) => ({
      ...prev,
      [newFieldName]: newField,
    }));

    setNewFieldName('');
    setNewFieldType('string');
    setNewFieldRequired(false);
    setArrayItemType('string');
  };

  const handleRemoveField = (fieldName: string) => {
    setFields((prev) => {
      const newFields = { ...prev };
      delete newFields[fieldName];
      return newFields;
    });
  };

  const handleAddSubField = (
    parentFieldName: string,
    subFieldName: string,
    subFieldType: string,
    subFieldRequired: boolean
  ) => {
    if (!subFieldName.trim()) return;

    setFields((prev) => {
      const newFields = { ...prev };
      const parentField = newFields[parentFieldName];

      if (parentField.type === 'object' && parentField.properties) {
        const newSubField: TemplateField = {
          type: subFieldType,
          required: subFieldRequired,
        };

        if (subFieldType === 'object') {
          newSubField.properties = {};
        } else if (subFieldType === 'array') {
          newSubField.items = { type: 'string', required: false };
        }

        parentField.properties[subFieldName] = newSubField;
      }

      return newFields;
    });
  };

  const handleRemoveSubField = (
    parentFieldName: string,
    subFieldName: string
  ) => {
    setFields((prev) => {
      const newFields = { ...prev };
      const parentField = newFields[parentFieldName];

      if (parentField.type === 'object' && parentField.properties) {
        delete parentField.properties[subFieldName];
      }

      return newFields;
    });
  };

  const handleSubmit = async () => {
    if (!name.trim() || !documentType || Object.keys(fields).length === 0) {
      return;
    }

    await onSubmit({
      name: name.trim(),
      description: description.trim() || undefined,
      documentType,
      fields,
    });

    // Reset form
    setName('');
    setDescription('');
    setDocumentType('');
    setFields({});
    setNewFieldName('');
    setNewFieldType('string');
    setNewFieldRequired(false);
  };

  const renderField = (
    fieldName: string,
    field: TemplateField,
    level: number = 0
  ) => {
    const indent = level * 20;

    return (
      <div key={fieldName} style={{ marginLeft: `${indent}px` }}>
        <div className="flex items-center gap-2 p-2 border rounded mb-2">
          <span className="font-medium">{fieldName}</span>
          <Badge variant="outline">{field.type}</Badge>
          {field.required && <Badge variant="destructive">Required</Badge>}

          {field.type === 'array' && field.items && (
            <Badge variant="secondary">Items: {field.items.type}</Badge>
          )}

          <Button
            size="sm"
            variant="ghost"
            onClick={() => handleRemoveField(fieldName)}
            className="ml-auto"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Render object properties */}
        {field.type === 'object' && field.properties && (
          <div className="ml-4 mb-2">
            {Object.entries(field.properties).map(([subFieldName, subField]) =>
              renderField(subFieldName, subField, level + 1)
            )}
            <SubFieldAdder parentFieldName={fieldName} />
          </div>
        )}

        {/* Render array item structure if it's an object */}
        {field.type === 'array' &&
          field.items?.type === 'object' &&
          field.items.properties && (
            <div className="ml-4 mb-2">
              <div className="text-sm text-gray-600 mb-2">
                Array item structure:
              </div>
              {Object.entries(field.items.properties).map(
                ([subFieldName, subField]) =>
                  renderField(subFieldName, subField, level + 1)
              )}
              <SubFieldAdder parentFieldName={fieldName} isArrayItem />
            </div>
          )}
      </div>
    );
  };

  const SubFieldAdder = ({
    parentFieldName,
    isArrayItem = false,
  }: {
    parentFieldName: string;
    isArrayItem?: boolean;
  }) => {
    const [subFieldName, setSubFieldName] = useState('');
    const [subFieldType, setSubFieldType] = useState('string');
    const [subFieldRequired, setSubFieldRequired] = useState(false);

    const handleAddSub = () => {
      if (!subFieldName.trim()) return;

      if (isArrayItem) {
        // Add to array items properties
        setFields((prev) => {
          const newFields = { ...prev };
          const parentField = newFields[parentFieldName];

          if (
            parentField.type === 'array' &&
            parentField.items?.type === 'object'
          ) {
            if (!parentField.items.properties) {
              parentField.items.properties = {};
            }

            const newSubField: TemplateField = {
              type: subFieldType,
              required: subFieldRequired,
            };

            if (subFieldType === 'object') {
              newSubField.properties = {};
            } else if (subFieldType === 'array') {
              newSubField.items = { type: 'string', required: false };
            }

            parentField.items.properties[subFieldName] = newSubField;
          }

          return newFields;
        });
      } else {
        handleAddSubField(
          parentFieldName,
          subFieldName,
          subFieldType,
          subFieldRequired
        );
      }

      setSubFieldName('');
      setSubFieldType('string');
      setSubFieldRequired(false);
    };

    return (
      <div className="p-2 border rounded-md bg-gray-50 mt-2">
        <div className="grid grid-cols-2 gap-2 mb-2">
          <Input
            value={subFieldName}
            onChange={(e) => setSubFieldName(e.target.value)}
            placeholder="Sub-field name"
            size="sm"
          />
          <select
            value={subFieldType}
            onChange={(e) => setSubFieldType(e.target.value)}
            className="h-8 px-2 py-1 text-sm border border-input rounded-md bg-white"
          >
            {FIELD_TYPES.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>
        <div className="flex items-center gap-2">
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={subFieldRequired}
              onChange={(e) => setSubFieldRequired(e.target.checked)}
            />
            Required
          </label>
          <Button
            size="sm"
            onClick={handleAddSub}
            disabled={!subFieldName.trim()}
            className="ml-auto"
          >
            <Plus className="h-3 w-3 mr-1" />
            Add
          </Button>
        </div>
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Template</DialogTitle>
          <DialogDescription>
            Create a custom template for extracting data from your documents.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium">Template Name</label>
            <Input
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="e.g., Custom Invoice Template"
            />
          </div>

          <div>
            <label className="text-sm font-medium">
              Description (Optional)
            </label>
            <Input
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe what this template is for..."
            />
          </div>

          <div>
            <label className="text-sm font-medium">Document Type</label>
            <select
              value={documentType}
              onChange={(e) => setDocumentType(e.target.value)}
              className="w-full h-9 px-3 py-1 text-sm border border-input rounded-md bg-transparent"
            >
              <option value="">Select document type...</option>
              {DOCUMENT_TYPES.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="text-sm font-medium">Fields</label>
            <div className="space-y-2">
              {Object.entries(fields).map(([fieldName, field]) =>
                renderField(fieldName, field)
              )}
            </div>

            <div className="mt-3 p-3 border rounded-md bg-gray-50">
              <div className="grid grid-cols-2 gap-2 mb-2">
                <Input
                  value={newFieldName}
                  onChange={(e) => setNewFieldName(e.target.value)}
                  placeholder="Field name"
                />
                <select
                  value={newFieldType}
                  onChange={(e) => setNewFieldType(e.target.value)}
                  className="h-9 px-3 py-1 text-sm border border-input rounded-md bg-white"
                >
                  {FIELD_TYPES.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>

              {newFieldType === 'array' && (
                <div className="mb-2">
                  <label className="text-sm font-medium">Array Item Type</label>
                  <select
                    value={arrayItemType}
                    onChange={(e) => setArrayItemType(e.target.value)}
                    className="w-full h-9 px-3 py-1 text-sm border border-input rounded-md bg-white"
                  >
                    {FIELD_TYPES.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              <div className="flex items-center gap-2">
                <label className="flex items-center gap-2 text-sm">
                  <input
                    type="checkbox"
                    checked={newFieldRequired}
                    onChange={(e) => setNewFieldRequired(e.target.checked)}
                  />
                  Required field
                </label>
                <Button
                  size="sm"
                  onClick={handleAddField}
                  disabled={!newFieldName.trim()}
                  className="ml-auto"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add Field
                </Button>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={
              loading ||
              !name.trim() ||
              !documentType ||
              Object.keys(fields).length === 0
            }
          >
            {loading ? 'Creating...' : 'Create Template'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
